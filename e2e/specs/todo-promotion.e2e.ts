/**
 * End-to-end tests for todo promotion functionality
 */

import { test, expect, describe } from 'vitest';
import {
  getFileContent,
  fileExists,
  waitForAsyncOperation,
  createTestFolders
} from '../helpers/task-sync-setup';
import { setupE2ETestHooks, executeCommand } from '../helpers/shared-context';

describe('Todo Promotion E2E', () => {
  const context = setupE2ETestHooks();

  test('should promote incomplete todo to task', async () => {
    await createTestFolders(context.page);

    // Create a test file with a todo item
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      const testContent = `# Test Area

Some content here.

- [ ] Buy groceries for the week
- Regular list item
- [x] Already completed task

More content.`;

      await app.vault.create('Areas/Personal.md', testContent);
    });

    // Open the file
    await context.page.keyboard.press('Control+O');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', 'Personal.md');
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(1000);

    // Position cursor on the todo line
    await context.page.click('.cm-editor');
    await context.page.keyboard.press('Control+G'); // Go to line
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', '5'); // Line with the todo
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(500);

    // Execute the promote todo command
    await executeCommand(context, 'Task Sync: Promote Todo to Task');
    await waitForAsyncOperation(2000);

    // Verify the task file was created
    const taskExists = await fileExists(context.page, 'Tasks/Buy groceries for the week.md');
    expect(taskExists).toBe(true);

    // Verify the task file content
    const taskContent = await getFileContent(context.page, 'Tasks/Buy groceries for the week.md');
    expect(taskContent).toContain('Buy groceries for the week');
    expect(taskContent).toContain('Areas: Personal');
    expect(taskContent).toContain('Status: Backlog');

    // Verify the original file was updated
    const updatedContent = await getFileContent(context.page, 'Areas/Personal.md');
    expect(updatedContent).toContain('- [[Buy groceries for the week]]');
    expect(updatedContent).not.toContain('- [ ] Buy groceries for the week');
  });

  test('should promote completed todo to task', async () => {
    await createTestFolders(context.page);

    // Create a test file with a completed todo item
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      const testContent = `# Project Notes

- [x] Finish the documentation
- [ ] Another incomplete task`;

      await app.vault.create('Projects/Documentation.md', testContent);
    });

    // Open the file
    await context.page.keyboard.press('Control+O');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', 'Documentation.md');
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(1000);

    // Position cursor on the completed todo line
    await context.page.click('.cm-editor');
    await context.page.keyboard.press('Control+G');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', '3'); // Line with the completed todo
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(500);

    // Execute the promote todo command
    await executeCommand(context, 'Task Sync: Promote Todo to Task');
    await waitForAsyncOperation(2000);

    // Verify the task file was created
    const taskExists = await fileExists(context.page, 'Tasks/Finish the documentation.md');
    expect(taskExists).toBe(true);

    // Verify the task file content shows completed status
    const taskContent = await getFileContent(context.page, 'Tasks/Finish the documentation.md');
    expect(taskContent).toContain('Finish the documentation');
    expect(taskContent).toContain('Project: Documentation');
    expect(taskContent).toContain('Status: Done');

    // Verify the original file preserves completion state
    const updatedContent = await getFileContent(context.page, 'Projects/Documentation.md');
    expect(updatedContent).toContain('- [x] [[Finish the documentation]]');
    expect(updatedContent).not.toContain('- [x] Finish the documentation');
  });

  test('should handle indented todo items', async () => {
    await createTestFolders(context.page);

    // Create a test file with indented todo items
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      const testContent = `# Nested Tasks

- Main item
  - [ ] Nested todo item
    - [ ] Deeply nested todo
- Another main item`;

      await app.vault.create('Areas/Work.md', testContent);
    });

    // Open the file
    await context.page.keyboard.press('Control+O');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', 'Work.md');
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(1000);

    // Position cursor on the nested todo line
    await context.page.click('.cm-editor');
    await context.page.keyboard.press('Control+G');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', '4'); // Line with nested todo
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(500);

    // Execute the promote todo command
    await executeCommand(context, 'Task Sync: Promote Todo to Task');
    await waitForAsyncOperation(2000);

    // Verify the task file was created
    const taskExists = await fileExists(context.page, 'Tasks/Nested todo item.md');
    expect(taskExists).toBe(true);

    // Verify the original file preserves indentation
    const updatedContent = await getFileContent(context.page, 'Areas/Work.md');
    expect(updatedContent).toContain('  - [[Nested todo item]]');
    expect(updatedContent).not.toContain('  - [ ] Nested todo item');
  });

  test('should show notice when no todo found', async () => {
    await createTestFolders(context.page);

    // Create a test file without todo items
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      const testContent = `# Regular Content

This is just regular text.
- Regular list item
- Another regular item`;

      await app.vault.create('Areas/Test.md', testContent);
    });

    // Open the file
    await context.page.keyboard.press('Control+O');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', 'Test.md');
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(1000);

    // Position cursor on a regular line
    await context.page.click('.cm-editor');
    await context.page.keyboard.press('Control+G');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', '3'); // Line with regular text
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(500);

    // Execute the promote todo command
    await executeCommand(context, 'Task Sync: Promote Todo to Task');
    await waitForAsyncOperation(1000);

    // Verify notice is shown (we can't easily test the notice content in e2e,
    // but we can verify no task was created)
    const tasksExist = await fileExists(context.page, 'Tasks');
    if (tasksExist) {
      // If Tasks folder exists, check it's empty or doesn't contain unexpected files
      const taskFiles = await context.page.evaluate(async () => {
        const app = (window as any).app;
        const folder = app.vault.getAbstractFileByPath('Tasks');
        if (!folder || !folder.children) return [];
        return folder.children
          .filter((child: any) => child.extension === 'md')
          .map((child: any) => child.name);
      });

      // Should not contain a task file for regular text
      expect(taskFiles).not.toContain('This is just regular text.md');
    }
  });

  test('should work with different list markers', async () => {
    await createTestFolders(context.page);

    // Create a test file with different list markers
    await context.page.evaluate(async () => {
      const app = (window as any).app;
      const testContent = `# Mixed List Markers

- [ ] Dash todo item
* [ ] Asterisk todo item
- [x] Completed dash item
* [x] Completed asterisk item`;

      await app.vault.create('Areas/Mixed.md', testContent);
    });

    // Open the file
    await context.page.keyboard.press('Control+O');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', 'Mixed.md');
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(1000);

    // Test asterisk todo
    await context.page.click('.cm-editor');
    await context.page.keyboard.press('Control+G');
    await context.page.waitForSelector('.prompt-input');
    await context.page.fill('.prompt-input', '4'); // Asterisk todo line
    await context.page.keyboard.press('Enter');
    await waitForAsyncOperation(500);

    await executeCommand(context, 'Task Sync: Promote Todo to Task');
    await waitForAsyncOperation(2000);

    // Verify the task was created and original line updated
    const taskExists = await fileExists(context.page, 'Tasks/Asterisk todo item.md');
    expect(taskExists).toBe(true);

    const updatedContent = await getFileContent(context.page, 'Areas/Mixed.md');
    expect(updatedContent).toContain('* [[Asterisk todo item]]');
    expect(updatedContent).not.toContain('* [ ] Asterisk todo item');
  });
});
